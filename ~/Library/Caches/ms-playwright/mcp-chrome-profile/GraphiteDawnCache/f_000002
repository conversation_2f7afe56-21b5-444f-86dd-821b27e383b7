bT      
                    #ifdef __clang__
                    #pragma clang diagnostic ignored "-Wall"
                    #endif
                #include <metal_stdlib>
using namespace metal;

template<typename T, size_t N>
struct tint_array {
  const constant T& operator[](size_t i) const constant { return elements[i]; }
  device T& operator[](size_t i) device { return elements[i]; }
  const device T& operator[](size_t i) const device { return elements[i]; }
  thread T& operator[](size_t i) thread { return elements[i]; }
  const thread T& operator[](size_t i) const thread { return elements[i]; }
  threadgroup T& operator[](size_t i) threadgroup { return elements[i]; }
  const threadgroup T& operator[](size_t i) const threadgroup { return elements[i]; }
  T elements[N];
};

struct tint_struct {
  float4 tint_member;
  uint2 tint_member_1;
  float2 tint_member_2;
  float4 tint_member_3;
  float4 tint_member_4;
  float4 tint_member_5;
  float4 tint_member_6;
  float2 tint_member_7;
  float2 tint_member_8;
};

struct tint_struct_1 {
  float4 tint_member_9;
};

struct tint_struct_4 {
  /* 0x0000 */ float4 tint_member_12;
  /* 0x0010 */ float2 tint_member_13;
  /* 0x0018 */ tint_array<int8_t, 8> tint_pad;
  /* 0x0020 */ tint_array<float4, 4> tint_member_14;
  /* 0x0060 */ float4 tint_member_15;
  /* 0x0070 */ int tint_member_16;
  /* 0x0074 */ int tint_member_17;
  /* 0x0078 */ int tint_member_18;
  /* 0x007c */ tint_array<int8_t, 4> tint_pad_1;
  /* 0x0080 */ float2 tint_member_19;
  /* 0x0088 */ float tint_member_20;
  /* 0x008c */ tint_array<int8_t, 4> tint_pad_2;
  /* 0x0090 */ float4 tint_member_21;
  /* 0x00a0 */ float2 tint_member_22;
  /* 0x00a8 */ tint_array<int8_t, 8> tint_pad_3;
  /* 0x00b0 */ float4 tint_member_23;
};

struct tint_struct_3 {
  /* 0x0000 */ tint_array<tint_struct_4, 1> tint_member_11;
};

struct tint_struct_5 {
  /* 0x0000 */ float4 tint_member_25;
  /* 0x0010 */ float4 tint_member_26;
};

struct tint_struct_2 {
  const device tint_struct_3* tint_member_10;
  const constant tint_struct_5* tint_member_24;
  thread uint* tint_member_27;
  sampler tint_member_28;
  texture2d<float, access::sample> tint_member_29;
};

struct tint_struct_6 {
  float4 tint_member_30 [[color(0)]];
};

struct tint_struct_7 {
  uint2 tint_member_31 [[user(locn0)]] [[flat]];
  float2 tint_member_32 [[user(locn1)]];
  float4 tint_member_33 [[user(locn2)]];
  float4 tint_member_34 [[user(locn3)]];
  float4 tint_member_35 [[user(locn4)]];
  float4 tint_member_36 [[user(locn5)]];
  float2 tint_member_37 [[user(locn6)]];
  float2 tint_member_38 [[user(locn7)]];
};

void v(thread float2* const v_1, float2x2 v_2, float2 v_3, float2 v_4, float2 v_5, float2 v_6) {
  float2 const v_7 = (v_6 - v_4);
  bool const v_8 = all((v_7 > float2(0.0f)));
  if (v_8) {
    bool const v_9 = all((v_6 > float2(0.0f)));
    bool v_10 = false;
    if (v_9) {
      v_10 = true;
    } else {
      bool v_11 = false;
      if ((v_3.x > 0.0f)) {
        v_11 = (v_3.y < 0.0f);
      } else {
        v_11 = false;
      }
      v_10 = v_11;
    }
    if (v_10) {
      float2 const v_12 = (v_7 * v_5);
      float2 const v_13 = (1.0f / ((v_6 * v_6) + (v_3.x * v_3.x)));
      float2 const v_14 = (v_13 * v_12);
      float2 const v_15 = (v_14 * v_2);
      float const v_16 = dot(v_15, v_15);
      float const v_17 = rsqrt(v_16);
      float const v_18 = v_17;
      float const v_19 = dot(v_12, v_14);
      float const v_20 = ((0.5f * v_18) * (v_19 - 1.0f));
      float const v_21 = (((v_6.x * v_3.x) * v_13.x) * v_18);
      float2 v_22 = float2((v_21 - v_20), (v_21 + v_20));
      v_22.y = select(-(v_22.y), 1.0f, ((v_6.x - v_3.x) <= 0.0f));
      float2 const v_23 = min((*v_1), v_22);
      (*v_1) = v_23;
    } else {
      if ((v_3.y == 0.0f)) {
        float2 const v_24 = (v_5 * v_2);
        float const v_25 = dot(v_24, v_24);
        float const v_26 = rsqrt(v_25);
        float const v_27 = (((v_3.x - v_7.x) - v_7.y) * v_26);
        float const v_28 = min((*v_1).x, v_27);
        (*v_1).x = v_28;
      }
    }
  }
}

float3 v_29(float3 v_30) {
  float const v_31 = (v_30.x * 0.01745329238474369049f);
  float const v_32 = cos(v_31);
  float const v_33 = (v_30.x * 0.01745329238474369049f);
  float const v_34 = sin(v_33);
  return float3(v_30.z, (v_30.y * v_32), (v_30.y * v_34));
}

float3 v_35(float3 v_36) {
  float3 v_37 = v_36;
  float const v_38 = v_37.x;
  float const v_39 = (v_38 - (360.0f * floor((v_38 / 360.0f))));
  v_37.x = v_39;
  if ((v_37.x < 0.0f)) {
    v_37.x = (v_37.x + 360.0f);
  }
  v_37 = float3(v_37.x, (v_37.yz * 0.00999999977648258209f));
  float3 const v_40 = (float3(0.0f, 8.0f, 4.0f) + (v_37.x * 0.03333333507180213928f));
  float3 const v_41 = (v_40 - (12.0f * floor((v_40 / 12.0f))));
  float3 const v_42 = v_41;
  float const v_43 = min(v_37.z, (1.0f - v_37.z));
  float const v_44 = (v_37.y * v_43);
  float3 const v_45 = min((v_42 - 3.0f), (9.0f - v_42));
  float3 const v_46 = clamp(v_45, float3(-1.0f), float3(1.0f));
  return (v_37.z - (v_44 * v_46));
}

float3 v_47(float3 v_48) {
  float3 v_49 = 0.0f;
  v_49.y = ((v_48.x + 16.0f) * 0.00862068962305784225f);
  v_49.x = ((v_48.y * 0.00200000009499490261f) + v_49.y);
  v_49.z = (v_49.y - (v_48.z * 0.00499999988824129105f));
  float3 const v_50 = powr(v_49, float3(3.0f));
  float3 const v_51 = v_50;
  float v_52 = 0.0f;
  if ((v_51.x > 0.00885645207017660141f)) {
    v_52 = v_51.x;
  } else {
    v_52 = (((116.0f * v_49.x) - 16.0f) * 0.00110705639235675335f);
  }
  float v_53 = 0.0f;
  if ((v_48.x > 8.00000095367431640625f)) {
    v_53 = v_51.y;
  } else {
    v_53 = (v_48.x * 0.00110705639235675335f);
  }
  float v_54 = 0.0f;
  if ((v_51.z > 0.00885645207017660141f)) {
    v_54 = v_51.z;
  } else {
    v_54 = (((116.0f * v_49.z) - 16.0f) * 0.00110705639235675335f);
  }
  float3 const v_55 = float3(v_52, v_53, v_54);
  return (v_55 * float3(0.96429562568664550781f, 1.0f, 0.82510453462600708008f));
}

float3 v_56(float3 v_57) {
  float const v_58 = ((v_57.x + (0.39633777737617492676f * v_57.y)) + (0.21580375730991363525f * v_57.z));
  float const v_59 = ((v_57.x - (0.10556134581565856934f * v_57.y)) - (0.06385417282581329346f * v_57.z));
  float const v_60 = ((v_57.x - (0.08948417752981185913f * v_57.y)) - (1.29148554801940917969f * v_57.z));
  float const v_61 = ((v_58 * v_58) * v_58);
  float const v_62 = ((v_59 * v_59) * v_59);
  float const v_63 = ((v_60 * v_60) * v_60);
  return float3((((4.07674169540405273438f * v_61) - (3.30771160125732421875f * v_62)) + (0.23096993565559387207f * v_63)), (((-1.26843798160552978516f * v_61) + (2.60975742340087890625f * v_62)) - (0.3413193821907043457f * v_63)), (((-0.00419608643278479576f * v_61) - (0.70341861248016357422f * v_62)) + (1.70761466026306152344f * v_63)));
}

float3 v_64(float3 v_65) {
  float3 v_66 = v_65;
  float2 const v_67 = v_66.yz;
  float v_68 = 0.0f;
  float2 v_69 = 0.0f;
  float2 v_70 = 0.0f;
  float const v_71 = dot(v_67, float2(0.40970200300216674805f, -0.91221898794174194336f));
  if ((v_71 < 0.0f)) {
    float const v_72 = dot(v_67, float2(0.46027600765228271484f, 0.88777601718902587891f));
    if ((v_72 < 0.0f)) {
      float const v_73 = dot(v_67, float2(-0.17112199962139129639f, 0.98524999618530273438f));
      if ((v_73 < 0.0f)) {
        v_68 = 0.1020469963550567627f;
        v_69 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
        v_70 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
      } else {
        v_68 = 0.09202899783849716187f;
        v_69 = float2(-0.03853299841284751892f, -0.00164999999105930328f);
        v_70 = float2(-0.23257200419902801514f, -0.09433099627494812012f);
      }
    } else {
      float const v_74 = dot(v_67, float2(0.94792497158050537109f, 0.31849500536918640137f));
      if ((v_74 < 0.0f)) {
        v_68 = 0.08170899748802185059f;
        v_69 = float2(-0.03460099920630455017f, -0.0022150001022964716f);
        v_70 = float2(0.01218499988317489624f, 0.33803099393844604492f);
      } else {
        v_68 = 0.09113200008869171143f;
        v_69 = float2(0.07037000358104705811f, 0.03413899987936019897f);
        v_70 = float2(0.01816999912261962891f, 0.37854999303817749023f);
      }
    }
  } else {
    float const v_75 = dot(v_67, float2(-0.9067999720573425293f, 0.42156198620796203613f));
    if ((v_75 < 0.0f)) {
      float const v_76 = dot(v_67, float2(-0.39791899919509887695f, -0.91742098331451416016f));
      if ((v_76 < 0.0f)) {
        v_68 = 0.11390200257301330566f;
        v_69 = float2(0.09083600342273712158f, 0.03625100106000900269f);
        v_70 = float2(0.22678099572658538818f, 0.01876400038599967957f);
      } else {
        v_68 = 0.1617390066385269165f;
        v_69 = float2(-0.00820199958980083466f, -0.26481899619102478027f);
        v_70 = float2(0.18715600669384002686f, -0.28430399298667907715f);
      }
    } else {
      v_68 = 0.1020469963550567627f;
      v_69 = float2(-0.01480400003492832184f, -0.16260799765586853027f);
      v_70 = float2(-0.27678599953651428223f, 0.00419300002977252007f);
    }
  }
  float v_77 = 1.0f;
  float const v_78 = dot(v_69, v_67);
  float const v_79 = v_78;
  if ((v_79 > 0.0f)) {
    float const v_80 = (1.0f - v_66.x);
    float const v_81 = (v_68 * v_80);
    if ((v_81 < v_79)) {
      float const v_82 = min(v_77, (v_81 / v_79));
      v_77 = v_82;
    }
  }
  float const v_83 = dot(v_70, v_67);
  float const v_84 = v_83;
  if ((v_84 > 0.0f)) {
    float const v_85 = v_66.x;
    float const v_86 = (v_68 * v_85);
    if ((v_86 < v_84)) {
      float const v_87 = min(v_77, (v_86 / v_84));
      v_77 = v_87;
    }
  }
  v_66 = float3(v_66.x, (v_66.yz * v_77));
  float3 const v_88 = v_56(v_66);
  return v_88;
}

void v_89(thread float2* const v_90, float2x2 v_91, float2 v_92, float4 v_93, float4 v_94, float4 v_95) {
  float2 v_96 = (*v_90);
  v((&v_96), v_91, v_92, v_93.xy, float2(-1.0f), float2(v_94.x, v_95.x));
  (*v_90) = v_96;
  float2 v_97 = (*v_90);
  v((&v_97), v_91, v_92, v_93.zy, float2(1.0f, -1.0f), float2(v_94.y, v_95.y));
  (*v_90) = v_97;
  float2 v_98 = (*v_90);
  v((&v_98), v_91, v_92, v_93.zw, float2(1.0f), float2(v_94.z, v_95.z));
  (*v_90) = v_98;
  float2 v_99 = (*v_90);
  v((&v_99), v_91, v_92, v_93.xw, float2(-1.0f, 1.0f), float2(v_94.w, v_95.w));
  (*v_90) = v_99;
}

float4 v_100(float4 v_101, int v_102, int v_103) {
  float4 v_104 = v_101;
  if (bool(v_103)) {
    switch(v_102) {
      case 2:
      case 3:
      case 4:
      {
        float const v_105 = max(v_104.w, 0.00009999999747378752f);
        v_104 = float4((v_104.xyz / v_105), v_104.w);
        break;
      }
      case 5:
      case 6:
      case 7:
      case 9:
      case 10:
      {
        float const v_106 = max(v_104.w, 0.00009999999747378752f);
        v_104 = float4(v_104.x, (v_104.yz / v_106), v_104.w);
        break;
      }
      default:
      {
        break;
      }
    }
  }
  switch(v_102) {
    case 2:
    {
      float3 const v_107 = v_47(v_104.xyz);
      v_104 = float4(v_107, v_104.w);
      break;
    }
    case 3:
    {
      float3 const v_108 = v_56(v_104.xyz);
      v_104 = float4(v_108, v_104.w);
      break;
    }
    case 4:
    {
      float3 const v_109 = v_64(v_104.xyz);
      v_104 = float4(v_109, v_104.w);
      break;
    }
    case 5:
    {
      float3 const v_110 = v_29(v_104.xyz);
      float3 const v_111 = v_47(v_110);
      v_104 = float4(v_111, v_104.w);
      break;
    }
    case 6:
    {
      float3 const v_112 = v_29(v_104.xyz);
      float3 const v_113 = v_56(v_112);
      v_104 = float4(v_113, v_104.w);
      break;
    }
    case 7:
    {
      float3 const v_114 = v_29(v_104.xyz);
      float3 const v_115 = v_64(v_114);
      v_104 = float4(v_115, v_104.w);
      break;
    }
    case 9:
    {
      float3 const v_116 = v_35(v_104.xyz);
      v_104 = float4(v_116, v_104.w);
      break;
    }
    case 10:
    {
      float3 v_117 = v_104.xyz;
      float3 v_118 = 0.0f;
      v_117 = float3(v_117.x, (v_117.yz * 0.00999999977648258209f));
      if (((v_117.y + v_117.z) >= 1.0f)) {
        v_118 = float3((v_117.y / (v_117.y + v_117.z)));
      } else {
        float3 const v_119 = v_35(float3(v_117.x, 100.0f, 50.0f));
        v_118 = v_119;
        v_118 = (v_118 * ((1.0f - v_117.y) - v_117.z));
        v_118 = (v_118 + v_117.y);
      }
      v_104 = float4(v_118, v_104.w);
      break;
    }
    default:
    {
      break;
    }
  }
  return v_104;
}

float2 v_120(int v_121, float2 v_122) {
  float2 v_123 = v_122;
  switch(v_121) {
    case 0:
    {
      float const v_124 = saturate(v_123.x);
      v_123.x = v_124;
      break;
    }
    case 1:
    {
      float const v_125 = fract(v_123.x);
      v_123.x = v_125;
      break;
    }
    case 2:
    {
      float const v_126 = (v_123.x - 1.0f);
      float const v_127 = floor((v_126 * 0.5f));
      v_123.x = ((v_126 - (2.0f * v_127)) - 1.0f);
      if (false) {
        float const v_128 = clamp(v_123.x, -1.0f, 1.0f);
        v_123.x = v_128;
      }
      float const v_129 = abs(v_123.x);
      v_123.x = v_129;
      break;
    }
    case 3:
    {
      bool v_130 = false;
      if ((v_123.x < 0.0f)) {
        v_130 = true;
      } else {
        v_130 = (v_123.x > 1.0f);
      }
      if (v_130) {
        return float2(0.0f, -1.0f);
      }
      break;
    }
    default:
    {
      break;
    }
  }
  return v_123;
}

float4 v_131(tint_array<float4, 4> v_132, float4 v_133, float2 v_134) {
  if ((v_134.y < 0.0f)) {
    return float4(0.0f);
  } else {
    if ((v_134.x <= v_133.x)) {
      return float4(v_132[0]);
    } else {
      if ((v_134.x < v_133.y)) {
        float4 const v_135 = mix(v_132[0], v_132[1], float4(((v_134.x - v_133.x) / (v_133.y - v_133.x))));
        return float4(v_135);
      } else {
        if ((v_134.x < v_133.z)) {
          float4 const v_136 = mix(v_132[1], v_132[2], float4(((v_134.x - v_133.y) / (v_133.z - v_133.y))));
          return float4(v_136);
        } else {
          if ((v_134.x < v_133.w)) {
            float4 const v_137 = mix(v_132[2], v_132[3], float4(((v_134.x - v_133.z) / (v_133.w - v_133.z))));
            return float4(v_137);
          } else {
            return float4(v_132[3]);
          }
        }
      }
    }
  }
  /* unreachable */
  return 0.0f;
}

float4 v_138(float4 v_139) {
  float const v_140 = max(v_139.w, 0.00009999999747378752f);
  return float4((v_139.xyz / v_140), v_139.w);
}

void v_141(tint_struct v_142, thread tint_struct_1* const v_143, tint_struct_2 v_144) {
  (*v_144.tint_member_27) = v_142.tint_member_1.y;
  float2 v_145 = float2((v_142.tint_member_2.x + 0.00000999999974737875f), 1.0f);
  float2 const v_146 = v_120((*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_16, v_145);
  v_145 = v_146;
  float4 const v_147 = v_131((*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_14, (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_15, v_145);
  float4 const v_148 = v_147;
  float4 const v_149 = v_100(v_148, (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_17, (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_18);
  float4 v_150 = v_149;
  float2 const v_151 = (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_19;
  if ((v_151.x < 0.0f)) {
    float4 const v_152 = v_138(v_150);
    v_150 = v_152;
  } else {
    float const v_153 = v_151.x;
    float const v_154 = v_151.y;
    float const v_155 = max(v_150.w, v_153);
    v_150.w = v_155;
    float const v_156 = max(v_150.w, v_154);
    v_150 = float4((v_150.xyz * v_156), v_150.w);
  }
  float const v_157 = (v_144.tint_member_29.sample(v_144.tint_member_28, (v_142.tint_member.xy * 0.125f), bias(clamp(-0.47499999403953552246f, -16.0f, 15.9899997711181640625f))).x - 0.5f);
  float3 const v_158 = (v_150.xyz + (v_157 * (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_20));
  float3 const v_159 = clamp(v_158, float3(0.0f), float3(v_150.w));
  float4 const v_160 = float4(v_159, v_150.w);
  float4 v_161 = float4(1.0f);
  float4 v_162 = 0.0f;
  if ((v_142.tint_member_8.x > 0.0f)) {
    v_162 = float4(1.0f);
  } else {
    if ((v_142.tint_member_8.y > 1.0f)) {
      float2 const v_163 = min(v_142.tint_member_4.xy, v_142.tint_member_4.zw);
      float2 const v_164 = v_163;
      float const v_165 = min(v_164.x, v_164.y);
      float const v_166 = (v_165 * v_142.tint_member.w);
      float const v_167 = ((v_142.tint_member_8.y - 1.0f) * v_142.tint_member.w);
      float const v_168 = (1.0f - (0.5f * v_167));
      float const v_169 = saturate((v_167 * (v_166 + v_168)));
      v_162 = float4(float(v_169));
    } else {
      float2 const v_170 = float2(v_142.tint_member_3.x, v_142.tint_member_3.y);
      float2x2 const v_171 = (float2x2(v_170, float2(v_142.tint_member_3.z, v_142.tint_member_3.w)) * (1.0f / v_142.tint_member.w));
      float2 const v_172 = (float2(1.0f, 0.0f) * v_171);
      float2 const v_173 = (float2(0.0f, 1.0f) * v_171);
      float const v_174 = dot(v_172, v_172);
      float const v_175 = rsqrt(v_174);
      float const v_176 = dot(v_173, v_173);
      float const v_177 = rsqrt(v_176);
      float2 const v_178 = float2(v_175, v_177);
      float2 const v_179 = min(v_142.tint_member_4.xy, v_142.tint_member_4.zw);
      float2 const v_180 = (v_178 * (v_142.tint_member_7.x + v_179));
      float const v_181 = min(v_180.x, v_180.y);
      float2 v_182 = float2(v_181, -1.0f);
      float v_183 = 0.0f;
      float v_184 = 0.0f;
      if ((v_142.tint_member_8.x > -0.94999998807907104492f)) {
        float2 const v_185 = (v_178 * ((v_142.tint_member_4.xy + v_142.tint_member_4.zw) + (2.0f * v_142.tint_member_7.xx)));
        float const v_186 = min(v_185.x, v_185.y);
        float const v_187 = min(v_186, 1.0f);
        v_183 = v_187;
        v_184 = (1.0f - (0.5f * v_183));
      } else {
        float2 const v_188 = ((2.0f * v_142.tint_member_7.x) * v_178);
        float2 const v_189 = (v_188 - v_180);
        float const v_190 = max(v_189.x, v_189.y);
        v_182.y = -(v_190);
        if ((v_142.tint_member_7.x > 0.0f)) {
          float const v_191 = min(v_188.x, v_188.y);
          float const v_192 = v_191;
          float2 const v_193 = select(float2(v_192), v_188, (v_189 >= float2(-0.5f)));
          float2 const v_194 = v_193;
          float const v_195 = max(v_194.x, v_194.y);
          float const v_196 = saturate(v_195);
          v_183 = v_196;
          v_184 = (1.0f - (0.5f * v_183));
        } else {
          v_184 = 1.0f;
          v_183 = v_184;
        }
      }
      float2 v_197 = v_182;
      v_89((&v_197), v_171, v_142.tint_member_7, v_142.tint_member_4, v_142.tint_member_5, v_142.tint_member_6);
      v_182 = v_197;
      float const v_198 = min(v_142.tint_member_8.y, 0.0f);
      float const v_199 = (v_198 * v_142.tint_member.w);
      float const v_200 = min((v_182.x + v_199), -(v_182.y));
      float const v_201 = (v_183 * (v_200 + v_184));
      float const v_202 = saturate(v_201);
      v_162 = float4(float(v_202));
    }
  }
  v_161 = v_162;
  float2 const v_203 = (v_142.tint_member.xy - (*v_144.tint_member_24).tint_member_25.xy);
  float4 const v_204 = (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_21;
  float2 const v_205 = (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_22;
  float4 const v_206 = (*v_144.tint_member_10).tint_member_11[(*v_144.tint_member_27)].tint_member_23;
  float const v_207 = abs(v_205.x);
  float2 const v_208 = float2(v_207);
  float2 const v_209 = (float2(v_206.xy) * ((v_204.xy + v_208) - v_203));
  float2 const v_210 = (float2(v_206.zw) * (v_203 - (v_204.zw - v_208)));
  float2 const v_211 = max(v_209, v_210);
  float2 const v_212 = max(v_211, float2(0.0f));
  float2 const v_213 = v_212;
  float const v_214 = length((v_213 * v_205.y));
  float const v_215 = saturate((v_208.x * (1.0f - v_214)));
  float const v_216 = float(v_215);
  float2 const v_217 = float2((v_203 - v_204.xy));
  float4 const v_218 = saturate(float4(v_217, float2((v_204.zw - v_203))));
  float4 v_219 = v_218;
  float4 const v_220 = mix(v_219, float4(1.0f), v_206);
  v_219 = v_220;
  float v_221 = ((((v_216 * v_219.x) * v_219.y) * v_219.z) * v_219.w);
  float v_222 = 0.0f;
  if ((v_205.x < 0.0f)) {
    v_222 = (1.0f - v_221);
  } else {
    v_222 = v_221;
  }
  v_221 = v_222;
  float4 const v_223 = float4(v_221);
  v_161 = (v_161 * v_223.w);
  (*v_143).tint_member_9 = (v_160 * v_161);
}

tint_struct_1 v_224(tint_struct v_225, tint_struct_2 v_226) {
  tint_struct_1 v_227 = {};
  v_141(v_225, (&v_227), v_226);
  return v_227;
}

fragment tint_struct_6 dawn_entry_point(float4 v_229 [[position]], tint_struct_7 v_230 [[stage_in]], const device tint_struct_3* v_231 [[buffer(2)]], const constant tint_struct_5* v_232 [[buffer(0)]], sampler v_233 [[sampler(0)]], texture2d<float, access::sample> v_234 [[texture(0)]]) {
  thread uint v_235 = 0u;
  tint_struct_2 const v_236 = tint_struct_2{.tint_member_10=v_231, .tint_member_24=v_232, .tint_member_27=(&v_235), .tint_member_28=v_233, .tint_member_29=v_234};
  tint_struct_6 v_237 = {};
  v_237.tint_member_30 = v_224(tint_struct{.tint_member=v_229, .tint_member_1=v_230.tint_member_31, .tint_member_2=v_230.tint_member_32, .tint_member_3=v_230.tint_member_33, .tint_member_4=v_230.tint_member_34, .tint_member_5=v_230.tint_member_35, .tint_member_6=v_230.tint_member_36, .tint_member_7=v_230.tint_member_37, .tint_member_8=v_230.tint_member_38}, v_236).tint_member_9;
  return v_237;
}
       dawn_entry_point                      