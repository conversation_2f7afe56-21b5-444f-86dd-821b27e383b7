# MCP NocoBase Development Guide

## Build & Test Commands
- `npm run build` - Compile TypeScript to dist/
- `npm run dev` - Run development server with tsx
- `npm run start` - Run compiled server from dist/
- `npm run test` - Run Jest tests
- `npm run test:mcp` - Run MCP-specific tests
- `npm run test:relations` - Test relation field functionality
- `npm run test:create` - Test record creation tools
- `npm run test:verify` - Run final verification tests
- `npm run test:all` - Run all test suites
- `npm run lint` - Run ESLint on src/**/*.ts
- `npm run lint:fix` - Fix ESLint issues automatically

## Code Style Guidelines

### TypeScript & Imports
- Use ES2022 target with ESNext modules
- Strict TypeScript configuration enabled
- Use verbatim module syntax
- Import statements should use ES module syntax
- Prefer named exports over default exports

### Naming Conventions
- **Collections**: Plural nouns, lowercase with underscores (`users`, `user_profiles`)
- **Variables**: camelCase for variables and functions
- **Interfaces**: PascalCase, prefix with I if needed (`NocoBaseConfig`)
- **Constants**: UPPER_SNAKE_CASE for constants
- **Files**: kebab-case for filenames

### Error Handling
- Use try-catch blocks for async operations
- Return structured error responses with clear messages
- Validate input parameters using Zod schemas
- Provide meaningful error messages for API failures

### Code Structure
- Separate tools by functionality in dedicated files
- Use interfaces for type definitions
- Keep functions small and focused
- Add JSDoc comments for complex functions

### Collection Standards (from Cursor rules)
- Collection names must be plural nouns in lowercase
- Titles and descriptions should use English
- Use standard categories: Administrative, Geography, User Management, etc.
- Validate naming before creating collections

### Testing
- Test files should be in tests/ directory
- Use JavaScript for integration tests
- Name test files descriptively (test-*.js)
- Include verification tests for critical functionality

### Git & Version Control
- .crush/ directory is ignored
- Commit messages should be descriptive
- Follow conventional commit format if possible