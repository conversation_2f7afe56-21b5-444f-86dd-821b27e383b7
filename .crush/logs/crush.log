{"time":"2025-08-12T22:44:49.827806+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-12T22:44:49.82943+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-12T22:44:50.214923+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (906.79µs)"}
{"time":"2025-08-12T22:44:50.21516+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (220.08µs)"}
{"time":"2025-08-12T22:44:50.215318+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (147.58µs)"}
{"time":"2025-08-12T22:44:50.215494+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (166.88µs)"}
{"time":"2025-08-12T22:44:50.215499+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-12T22:44:50.21554+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-12T22:44:50.244303+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-12T22:44:50.244352+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-12T22:44:50.269247+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-12T22:44:50.269342+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-12T22:44:50.452144+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-12T22:44:52.54521+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-12T22:44:52.54525+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-12T22:44:52.573182+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-12T22:44:52.573214+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-12T22:44:55.659411+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_38cf721bf4a94c49bb003b04","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:44:58.035913+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_fb4ed5dbc7df4b868fe41cfc","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:44:58.242171+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/tools.init.func1","file":"/home/<USER>/work/crush/crush/internal/llm/tools/rg.go","line":18},"msg":"Ripgrep (rg) not found in $PATH. Some grep features might be limited or slower."}
{"time":"2025-08-12T22:44:59.715588+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_0aed322805074df9b04b332d","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:02.109292+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_1c086b605ff947b9adedb8e4","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:04.81994+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_8108273dc6a046fd9bb70ea1","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:06.887495+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_5272b4e871544be596dc1563","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:09.320873+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_20334f01fa4740afb71e5d68","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:15.697194+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_9bd215d130b04294bf890ec7","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:17.415627+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_c5f93d98a5974b879250cf79","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:20.606171+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_dc3cabd383c544af89e7689f","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:23.692322+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_b6184e0c01da4302bf7449a0","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:25.530336+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_a805abd8ef674ecb82205f17","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:26.750695+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_5ae9bea7e65d41fca2b7a2e0","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:34.035334+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_9a08bd23621b40a0b41fecd2","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:45:46.172448+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_099d9306c7754ec18476d952","name":"edit","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:53:29.923622+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-12T22:53:29.924904+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-12T22:53:30.316367+08:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-12T22:53:30.317027+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-12T22:53:30.343586+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-12T22:53:30.484607+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-12T22:53:30.56751+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-12T22:53:30.603429+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":299},"msg":"Initialized mcp client","name":"nocobase"}
{"time":"2025-08-12T22:53:31.352641+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":299},"msg":"Initialized mcp client","name":"context7"}
{"time":"2025-08-12T22:53:31.370348+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":299},"msg":"Initialized mcp client","name":"asana"}
{"time":"2025-08-12T22:53:33.444089+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":299},"msg":"Initialized mcp client","name":"deep_graph"}
{"time":"2025-08-12T22:53:34.458534+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":299},"msg":"Initialized mcp client","name":"excel"}
{"time":"2025-08-12T22:53:37.603997+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.doGetMCPTools.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":299},"msg":"Initialized mcp client","name":"playwright"}
{"time":"2025-08-12T22:53:37.605532+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-12T22:53:37.605911+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":175},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-12T22:57:02.902409+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_85c001840f62427aab547022","name":"mcp_playwright_browser_navigate","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:57:16.961009+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_c25e9b7e9f6940dd926a626b","name":"mcp_playwright_browser_click","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:57:24.64371+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_611b9c36dec14d09984d82cd","name":"mcp_playwright_browser_tab_select","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:57:30.77554+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_bfe6d7588d5c4872aac728ff","name":"mcp_playwright_browser_tab_select","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:57:37.611164+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_7b2d6c07ab044ac688e752ba","name":"mcp_playwright_browser_click","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:57:45.529412+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_6f467be6a6b7442cb9376895","name":"mcp_playwright_browser_tab_select","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:57:54.068459+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_d58e18806b624f60a8df1926","name":"mcp_playwright_browser_click","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:58:01.757749+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_825787c3cbff4623a9045b35","name":"mcp_playwright_browser_tab_select","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:58:08.343063+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_c2679135d7be4c46b1db2c29","name":"mcp_playwright_browser_close","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:58:15.31774+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_96e06c2101db486b870958ca","name":"mcp_playwright_browser_close","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:58:21.994263+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_8a8ce652b79643688e1a8259","name":"mcp_playwright_browser_close","input":"","type":"","finished":false}}
{"time":"2025-08-12T22:58:29.08488+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_0c956f6b78bd4b499c1c8370","name":"mcp_playwright_browser_close","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:03:04.035902+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_8d119986b81248829532a72d","name":"mcp_nocobase_create_menu_group","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:03:16.425639+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_3aa16e1a26ef481aaec2810c","name":"mcp_nocobase_create_menu_group","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:03:21.75057+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_122b770231d34e9eb5fc7695","name":"mcp_nocobase_create_menu_page","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:03:26.845062+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_2554379ec7da4d32808f015a","name":"mcp_nocobase_list_routes","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:03:31.960625+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_7778b9fe22064a2fa8b30e13","name":"mcp_nocobase_list_collections","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:06:56.996644+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_36b53a7746c84c7bb00ed038","name":"mcp_nocobase_create_menu_group","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:07:01.978106+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_78925199904c48dda6eb0573","name":"mcp_nocobase_list_routes","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:07:09.695653+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":630},"msg":"Tool call started","toolCall":{"id":"call_32d8c6b8baef44bf8a7111c8","name":"mcp_nocobase_list_collections","input":"","type":"","finished":false}}
{"time":"2025-08-12T23:07:12.900029+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":241},"msg":"Request cancellation initiated","session_id":"39277e7a-2bde-4d94-8ae5-d182d10597ef"}
{"time":"2025-08-12T23:07:12.900198+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.mcpLogger.Errorf","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":340},"msg":"Error reading from stdout: read |0: file already closed"}
{"time":"2025-08-12T23:07:12.9003+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.mcpLogger.Errorf","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":340},"msg":"Error reading from stdout: read |0: file already closed"}
{"time":"2025-08-12T23:07:12.900331+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.mcpLogger.Errorf","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":340},"msg":"Error reading from stdout: read |0: file already closed"}
{"time":"2025-08-12T23:07:12.900357+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.mcpLogger.Errorf","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":340},"msg":"Error reading from stdout: read |0: file already closed"}
{"time":"2025-08-12T23:07:12.900402+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.mcpLogger.Errorf","file":"/home/<USER>/work/crush/crush/internal/llm/agent/mcp-tools.go","line":340},"msg":"Error reading from stdout: read |0: file already closed"}
